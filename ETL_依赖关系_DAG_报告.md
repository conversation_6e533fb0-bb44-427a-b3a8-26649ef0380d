# TDR ETL 作业依赖关系 DAG 报告

## 概述

本报告按照 dbt DAG 的风格，分析 TDR 项目中所有 ETL 作业的依赖关系，展示数据流转的完整链路。

## 数据分层架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据源层       │    │   ODS 层        │    │   DWD 层        │    │   DWM 层        │
│  (External)     │───▶│ (Raw Data)      │───▶│ (Clean Data)    │───▶│ (Business Logic)│
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                                                              │
                                                                              ▼
                                                                    ┌─────────────────┐
                                                                    │   DWS 层        │
                                                                    │ (Data Service)  │
                                                                    └─────────────────┘
```

## ODS 层数据表 (源数据层)

### 企业基础信息类
- `ods_enterprise` - 企业基本信息 (核心表)
- `ods_enterprise_certificate` - 企业证书信息
- `ods_enterprise_annual_qualification` - 企业年度资质
- `ods_enterprise_anomaly` - 企业异常信息

### 司法相关类
- `ods_judgment_document` - 裁判文书
- `ods_court_announcement` - 法院公告
- `ods_enforcement` - 执行信息
- `ods_punishment` - 行政处罚
- `ods_combined_punishment` - 联合惩戒
- `ods_punish_dishonesty` - 失信惩戒
- `ods_bankruptcy_reorganization` - 破产重整

### 认证相关类
- `ods_certificate` - 证书信息
- `ods_certification_authority` - 认证机构

### 推广营销类
- `ods_promotion` - 推广信息

## ETL 作业依赖关系图

### 1. 企业基础数据流

```mermaid
graph TD
    A[ods_enterprise] --> B[dwd_enterprise]
    B --> C[dwm_enterprise_promotion_competitor]
    B --> D[dwm_enterprise_risk]
```

### 2. 推广分析数据流

```mermaid
graph TD
    A[ods_promotion] --> B[dwm_enterprise_promotion_link]
    A --> C[dwm_enterprise_promotion_competitor]
    D[dwd_enterprise] --> C
    C --> E[dws_enterprise_promotion_stats]
    A --> E
```

### 3. 司法风险数据流

```mermaid
graph TD
    A[ods_judgment_document] --> B[dwm_enterprise_judgment_document]
    C[ods_court_announcement] --> D[dwm_enterprise_court_announcement]
    E[ods_enterprise_anomaly] --> F[dwm_enterprise_risk]
    G[dwd_enterprise] --> F
    B --> F
    D --> F
    H[ods_combined_punishment] --> F
    I[ods_enforcement] --> F
    J[ods_bankruptcy_reorganization] --> F
    K[ods_punish_dishonesty] --> F
```

### 4. 证书认证数据流

```mermaid
graph TD
    A[ods_certificate] --> B[dwm_enterprise_certificate_cal]
    C[ods_enterprise_certificate] --> B
    D[ods_certification_authority] --> B
    E[ods_enterprise_annual_qualification] --> F[dwm_enterprise_certificate_second_dimension]
    B --> F
```

## 详细依赖关系表

| 目标表 | 层级 | 依赖表 | 依赖层级 | 业务说明 |
|--------|------|--------|----------|----------|
| `dwd_enterprise` | DWD | `ods_enterprise` | ODS | 企业基础信息清洗 |
| `dwm_enterprise_promotion_competitor` | DWM | `ods_promotion`, `dwd_enterprise` | ODS, DWD | 推广竞对分析 |
| `dwm_enterprise_promotion_link` | DWM | `ods_promotion` | ODS | 推广链接分析 |
| `dwm_enterprise_judgment_document` | DWM | `ods_judgment_document` | ODS | 裁判文书分析 |
| `dwm_enterprise_court_announcement` | DWM | `ods_court_announcement` | ODS | 法院公告分析 |
| `dwm_enterprise_certificate_cal` | DWM | `ods_certificate`, `ods_enterprise_certificate`, `ods_certification_authority` | ODS | 证书计算分析 |
| `dwm_enterprise_certificate_second_dimension` | DWM | `ods_enterprise_annual_qualification`, `dwm_enterprise_certificate_cal` | ODS, DWM | 证书二维分析 |
| `dwm_enterprise_risk` | DWM | `ods_enterprise_anomaly`, `dwd_enterprise`, `dwm_enterprise_judgment_document`, `dwm_enterprise_court_announcement`, `ods_combined_punishment`, `ods_enforcement`, `ods_bankruptcy_reorganization`, `ods_punish_dishonesty` | ODS, DWD, DWM | 企业风险综合分析 |
| `dws_enterprise_promotion_stats` | DWS | `ods_promotion`, `dwm_enterprise_promotion_competitor` | ODS, DWM | 推广统计汇总 |

## 执行顺序建议

### Level 0 (ODS层 - 数据源)
所有 ODS 表都是数据源，无依赖关系，可并行执行：
- `ods_enterprise`
- `ods_promotion`
- `ods_judgment_document`
- `ods_court_announcement`
- `ods_enterprise_anomaly`
- `ods_combined_punishment`
- `ods_enforcement`
- `ods_bankruptcy_reorganization`
- `ods_punish_dishonesty`
- `ods_certificate`
- `ods_enterprise_certificate`
- `ods_certification_authority`
- `ods_enterprise_annual_qualification`

### Level 1 (DWD层 + 简单DWM)
依赖单个ODS表的作业：
- `dwd_enterprise` ← `ods_enterprise`
- `dwm_enterprise_promotion_link` ← `ods_promotion`
- `dwm_enterprise_judgment_document` ← `ods_judgment_document`
- `dwm_enterprise_court_announcement` ← `ods_court_announcement`

### Level 2 (复杂DWM)
依赖多个表或DWD层的作业：
- `dwm_enterprise_promotion_competitor` ← `ods_promotion` + `dwd_enterprise`
- `dwm_enterprise_certificate_cal` ← `ods_certificate` + `ods_enterprise_certificate` + `ods_certification_authority`

### Level 3 (高级DWM)
依赖其他DWM表的作业：
- `dwm_enterprise_certificate_second_dimension` ← `ods_enterprise_annual_qualification` + `dwm_enterprise_certificate_cal`
- `dwm_enterprise_risk` ← 多个ODS表 + `dwd_enterprise` + `dwm_enterprise_judgment_document` + `dwm_enterprise_court_announcement`

### Level 4 (DWS层)
最终的数据服务层：
- `dws_enterprise_promotion_stats` ← `ods_promotion` + `dwm_enterprise_promotion_competitor`

## 关键路径分析

### 最长依赖链路
```
ods_enterprise → dwd_enterprise → dwm_enterprise_risk (4层)
```

### 最复杂依赖节点
`dwm_enterprise_risk` 依赖 8 个上游表，是整个DAG中依赖关系最复杂的节点。

### 并行度分析
- **Level 0**: 13个表可并行执行
- **Level 1**: 4个作业可并行执行
- **Level 2**: 2个作业可并行执行
- **Level 3**: 2个作业可并行执行
- **Level 4**: 1个作业执行

## 性能优化建议

### 1. 并行执行策略
- 同一Level内的作业可以并行执行
- 合理分配计算资源，避免资源竞争

### 2. 关键路径优化
- 优先保证 `ods_enterprise` → `dwd_enterprise` 的执行效率
- `dwm_enterprise_risk` 作为复杂节点，需要特别关注其性能

### 3. 依赖管理
- 建议使用 Airflow 或类似的调度工具管理依赖关系
- 实现失败重试和依赖检查机制

### 4. 数据分区策略
- 按业务日期分区，支持增量处理
- 合理设置分区数量，平衡并行度和小文件问题

## 完整依赖关系图 (dbt风格)

```mermaid
graph TD
    %% ODS层 - 数据源
    subgraph "ODS Layer (Level 0)"
        A1[ods_enterprise]
        A2[ods_promotion]
        A3[ods_judgment_document]
        A4[ods_court_announcement]
        A5[ods_enterprise_anomaly]
        A6[ods_combined_punishment]
        A7[ods_enforcement]
        A8[ods_bankruptcy_reorganization]
        A9[ods_punish_dishonesty]
        A10[ods_certificate]
        A11[ods_enterprise_certificate]
        A12[ods_certification_authority]
        A13[ods_enterprise_annual_qualification]
    end

    %% DWD层 + 简单DWM
    subgraph "DWD/DWM Layer (Level 1)"
        B1[dwd_enterprise]
        B2[dwm_enterprise_promotion_link]
        B3[dwm_enterprise_judgment_document]
        B4[dwm_enterprise_court_announcement]
    end

    %% 复杂DWM
    subgraph "DWM Layer (Level 2)"
        C1[dwm_enterprise_promotion_competitor]
        C2[dwm_enterprise_certificate_cal]
    end

    %% 高级DWM
    subgraph "DWM Layer (Level 3)"
        D1[dwm_enterprise_certificate_second_dimension]
        D2[dwm_enterprise_risk]
    end

    %% DWS层
    subgraph "DWS Layer (Level 4)"
        E1[dws_enterprise_promotion_stats]
    end

    %% 依赖关系
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    A2 --> C1
    B1 --> C1
    A10 --> C2
    A11 --> C2
    A12 --> C2

    A13 --> D1
    C2 --> D1
    A5 --> D2
    B1 --> D2
    B3 --> D2
    B4 --> D2
    A6 --> D2
    A7 --> D2
    A8 --> D2
    A9 --> D2

    A2 --> E1
    C1 --> E1

    %% 样式
    classDef odsStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef dwdStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dwmStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dwsStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class A1,A2,A3,A4,A5,A6,A7,A8,A9,A10,A11,A12,A13 odsStyle
    class B1 dwdStyle
    class B2,B3,B4,C1,C2,D1,D2 dwmStyle
    class E1 dwsStyle
```

## 作业调度配置 (Airflow风格)

### DAG 配置示例

```python
# airflow_dag_config.py
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.spark_submit_operator import SparkSubmitOperator

default_args = {
    'owner': 'data_team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'tdr_etl_pipeline',
    default_args=default_args,
    description='TDR ETL Pipeline',
    schedule_interval='0 2 * * *',  # 每天凌晨2点执行
    catchup=False,
    max_active_runs=1
)

# Level 1 作业
dwd_enterprise = SparkSubmitOperator(
    task_id='dwd_enterprise',
    application='tdr/warehouse/dwd/dwd_enterprise/builder/spark/dwd_enterprise_compute.py',
    dag=dag
)

dwm_promotion_link = SparkSubmitOperator(
    task_id='dwm_enterprise_promotion_link',
    application='tdr/warehouse/dwm/enterprise/dwm_enterprise_promotion_link/builder/spark/dwm_enterprise_promotion_link_compute.py',
    dag=dag
)

# Level 2 作业
dwm_promotion_competitor = SparkSubmitOperator(
    task_id='dwm_enterprise_promotion_competitor',
    application='tdr/warehouse/dwm/enterprise/dwm_enterprise_promotion_competitor/builder/spark/dwm_enterprise_promotion_competitor_compute.py',
    dag=dag
)

# 设置依赖关系
dwd_enterprise >> dwm_promotion_competitor
```

## 数据质量监控

### 关键指标监控

| 监控项 | 指标 | 阈值 | 说明 |
|--------|------|------|------|
| 数据完整性 | 记录数量变化率 | ±20% | 检测数据异常波动 |
| 数据新鲜度 | 最新数据时间戳 | 24小时 | 确保数据及时更新 |
| 作业成功率 | 成功执行比例 | >95% | 监控作业稳定性 |
| 执行时长 | 作业运行时间 | 历史均值±50% | 检测性能异常 |
| 数据一致性 | 关联表记录匹配率 | >90% | 验证数据关联正确性 |

### 告警规则

```yaml
# 数据质量告警配置
alerts:
  - name: "数据量异常"
    condition: "record_count_change > 20%"
    severity: "high"
    notification: ["<EMAIL>"]

  - name: "作业执行失败"
    condition: "job_status = 'failed'"
    severity: "critical"
    notification: ["<EMAIL>", "<EMAIL>"]

  - name: "执行时间异常"
    condition: "execution_time > historical_avg * 1.5"
    severity: "medium"
    notification: ["<EMAIL>"]
```

## 故障恢复策略

### 1. 自动重试机制
- 失败作业自动重试 2-3 次
- 指数退避策略，避免系统过载
- 记录详细的失败日志

### 2. 数据回滚方案
- 保留历史版本数据，支持快速回滚
- 实现增量和全量处理的切换
- 建立数据备份和恢复流程

### 3. 依赖隔离
- 上游作业失败时，下游作业暂停执行
- 支持部分作业的独立重跑
- 实现作业级别的熔断机制

## 扩展性考虑

### 1. 新增数据源
- 标准化的 ODS 层接入流程
- 统一的数据格式和质量标准
- 自动化的 Schema 演进支持

### 2. 业务逻辑扩展
- 模块化的 DWM 层设计
- 可插拔的计算组件
- 标准化的业务规则配置

### 3. 性能扩展
- 支持动态资源分配
- 实现智能的分区策略
- 优化热点数据的处理

这个 ETL 依赖关系 DAG 为 TDR 项目提供了清晰的数据流转视图，有助于理解业务逻辑、优化执行性能和保障数据质量。
