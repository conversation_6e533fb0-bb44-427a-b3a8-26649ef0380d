# TDR 项目分析报告

## 项目概述

TDR (Tungee Data Repository) 是一个基于 Apache Spark 的企业数据仓库项目，主要用于处理和分析企业相关的各类数据。项目采用典型的数据仓库分层架构，包含 ODS、DWD、DWM、DWS 四个数据层。

## 项目架构

### 目录结构
```
tdr/
├── common/                    # 公共模块
│   ├── constant/             # 常量定义
│   ├── utils/               # 工具类
│   └── warehourse/          # 数据仓库工具
└── warehouse/               # 数据仓库主体
    ├── ods/                # 操作数据存储层
    ├── dwd/                # 数据仓库明细层
    ├── dwm/                # 数据仓库中间层
    ├── dws/                # 数据仓库服务层
    └── utils/              # 仓库工具类
```

### 数据分层架构

#### 1. ODS (Operational Data Store) - 操作数据存储层
存储原始业务数据，包含以下主要数据源：

**企业基础信息类：**
- `ods_enterprise` - 企业基本信息（核心表，包含6000+字段）
- `ods_enterprise_certificate` - 企业证书信息
- `ods_enterprise_annual_qualification` - 企业年度资质信息
- `ods_enterprise_anomaly` - 企业异常信息

**司法相关类：**
- `ods_judgment_document` - 裁判文书
- `ods_court_announcement` - 法院公告
- `ods_enforcement` - 执行信息
- `ods_punishment` - 行政处罚
- `ods_combined_punishment` - 联合惩戒
- `ods_punish_dishonesty` - 失信惩戒
- `ods_bankruptcy_reorganization` - 破产重整

**认证相关类：**
- `ods_certificate` - 证书信息
- `ods_certification_authority` - 认证机构

**推广营销类：**
- `ods_promotion` - 推广信息

#### 2. DWD (Data Warehouse Detail) - 数据仓库明细层
对 ODS 层数据进行清洗和标准化：

- `dwd_enterprise` - 清洗后的企业信息
- `dwd_court_announcement` - 清洗后的法院公告

#### 3. DWM (Data Warehouse Middle) - 数据仓库中间层
基于业务逻辑进行数据整合和计算：

**企业风险分析：**
- `dwm_enterprise_risk` - 企业经营风险综合分析
- `dwm_enterprise_judgment_document` - 企业裁判文书分析
- `dwm_enterprise_court_announcement` - 企业法院公告分析

**企业证书分析：**
- `dwm_enterprise_certificate_cal` - 企业证书计算分析
- `dwm_enterprise_certificate_second_dimension` - 企业证书二维分析

**企业推广分析：**
- `dwm_enterprise_promotion_competitor` - 企业推广竞对分析
- `dwm_enterprise_promotion_link` - 企业推广链接分析

#### 4. DWS (Data Warehouse Service) - 数据仓库服务层
面向业务应用的数据集市：

- `dws_enterprise_promotion_stats` - 企业推广统计分析

## 核心业务模块

### 1. 司法模块
处理企业相关的司法信息，包括：
- 裁判文书分析和风险评估
- 法院公告监控
- 执行信息跟踪
- 行政处罚记录
- 失信记录管理

### 2. 推广模块
分析企业推广营销活动：
- 推广竞对识别和分析
- 推广关键词统计
- 推广链接监控
- 推广效果评估

### 3. 证书认证模块
管理企业各类证书和资质：
- 证书有效性验证
- 证书到期提醒
- 认证机构管理
- 技术类证书特殊处理

## 技术栈

### 核心技术
- **Apache Spark** - 大数据处理引擎
- **PySpark** - Python Spark API
- **Python 3.6+** - 主要开发语言

### 数据格式
- **JSON** - 主要数据交换格式
- **BSON** - MongoDB 二进制格式支持
- **CSV** - 结构化数据导入

### 工具库
- **pymongo_spark** - MongoDB Spark 连接器
- **scikit-learn** - 机器学习算法（用于文本相似度计算）

## 公共模块详解

### 常量定义 (common/constant/)
- `common.py` - 通用常量（文件类型、时间格式等）
- `module.py` - 业务模块常量（企业、司法、推广等模块标识）

### 工具类 (common/utils/)
- `spark/spark_utils.py` - Spark 操作工具类
- `dict_lib.py` - 字典操作工具
- `time_helper.py` - 时间处理工具
- `md5_util.py` - MD5 加密工具
- `json_util.py` - JSON 处理工具

### 数据仓库工具 (common/warehourse/)
- `dict_utils.py` - 数据字典处理工具

## 数据处理流程

### 典型的 ETL 流程
1. **Extract** - 从各数据源提取原始数据到 ODS 层
2. **Transform** - 在 DWD 层进行数据清洗和标准化
3. **Load** - 在 DWM 层进行业务逻辑计算，在 DWS 层生成应用数据

### Spark 作业结构
每个数据处理模块都遵循统一的结构：
```
module_name/
├── meta/                    # 数据模型定义
│   └── module_name.py      # 字段定义和 Spark Schema
├── builder/                 # 数据构建逻辑
│   └── spark/              # Spark 计算逻辑
│       └── module_compute.py
└── utils/                   # 模块专用工具
    ├── constant.py         # 模块常量
    └── util.py            # 业务逻辑函数
```

## 关键特性

### 1. 企业风险评估
- 综合司法、行政处罚、失信等多维度数据
- 按年度统计风险事件
- 风险类型分类和统计
- 最新风险信息跟踪

### 2. 推广竞对分析
- 基于推广关键词识别竞争对手
- 推广活动时间分析
- 推广内容文本分析
- 竞对企业信息补充

### 3. 证书管理
- 证书有效期管理
- 技术类证书特殊标识
- 证书续办提醒
- 认证机构信息关联

### 4. 数据质量保障
- 统一的数据模型定义
- 严格的字段类型检查
- 数据去重和清洗
- 异常数据过滤

## 项目规模

- **总文件数**: 约 150+ Python 文件
- **核心数据表**: 20+ 个主要业务表
- **字段总数**: 企业表单表就包含 6000+ 字段
- **业务模块**: 涵盖企业、司法、推广、认证等多个领域

## 开发规范

### 命名规范
- 类名使用 PascalCase
- 方法名使用 snake_case
- 常量使用 UPPER_CASE
- 文件名使用 snake_case

### 代码结构
- 每个模块都有独立的 meta 定义
- 统一的 Spark Schema 定义
- 标准化的 ETL 处理流程
- 完整的字段注释和文档

这个项目是一个成熟的企业级数据仓库系统，专注于企业数据的全方位分析和处理。

## 详细模块分析

### ODS 层核心表详解

#### ods_enterprise (企业基础信息表)
这是项目中最重要的核心表，包含企业的全方位信息：

**基础信息字段：**
- `name` - 企业名称
- `socialCreditCode` - 统一社会信用代码
- `orgCode` - 组织机构代码
- `legalRepresentative` - 法定代表人
- `address` - 注册地址
- `registeredCapital` - 注册资本
- `foundTime` - 成立时间
- `businessTerm` - 营业期限

**经营信息字段：**
- `businessScope` - 经营范围
- `businessType` - 企业类型
- `registrationAuthority` - 登记机关
- `approvalDate` - 核准日期
- `enterpriseStatus` - 企业状态

**复杂结构字段：**
- `holderList` - 股东信息列表
- `memberList` - 主要人员列表
- `branchOfficeList` - 分支机构列表
- `alterationList` - 变更信息列表
- `anomalyList` - 经营异常列表
- `punishmentList` - 行政处罚列表

#### ods_promotion (推广信息表)
存储企业推广营销数据：
- `prmtEnterpriseName` - 推广企业名称
- `prmtKey` - 推广关键词
- `prmtText` - 推广文本内容
- `prmtLink` - 推广链接
- `prmtSource` - 推广来源
- `prmtTime` - 推广时间

### DWM 层业务逻辑详解

#### dwm_enterprise_risk (企业风险分析)
整合多源数据进行企业风险评估：

**风险统计字段：**
- `riskTypeStats` - 风险类型统计
- `annualRiskStats` - 年度风险统计
- `riskTypeCount` - 风险类型数量
- `latestRiskInfo` - 最新风险信息

**司法风险字段：**
- `jdRoleStats` - 裁判文书角色统计
- `jdCaseTypeStats` - 案件类型统计
- `hasJudgmentDocument` - 是否有裁判文书

**计算逻辑：**
1. 合并异常信息、处罚信息、执行信息等
2. 按风险类型进行分类统计
3. 按年度进行时间维度分析
4. 生成综合风险评分

#### dwm_enterprise_promotion_competitor (推广竞对分析)
基于推广数据识别和分析竞争对手：

**核心字段：**
- `nameId` - 企业标识
- `competitorId` - 竞对企业标识
- `competitorName` - 竞对企业名称
- `prmtKeys` - 共同推广关键词
- `competitorAddress` - 竞对地址信息

**计算逻辑：**
1. 按推广关键词聚合企业信息
2. 识别同一关键词下的多个企业为竞对关系
3. 计算竞对企业的推广活跃度
4. 补充竞对企业的基础信息

### 核心算法和业务规则

#### 1. 企业风险评估算法
```python
# 风险类型映射
RISK_TYPE_MAPPING = {
    'anomaly': '经营异常',
    'punishment': '行政处罚',
    'enforcement': '被执行',
    'dishonesty': '失信记录',
    'bankruptcy': '破产重整'
}

# 风险权重计算
def calculate_risk_score(risk_data):
    # 根据风险类型和数量计算综合评分
    # 考虑时间衰减因子
    # 返回标准化风险分数
```

#### 2. 推广竞对识别算法
```python
def identify_competitors(promotion_data):
    # 1. 按关键词分组推广数据
    # 2. 计算企业间的推广重叠度
    # 3. 使用文本相似度算法优化匹配
    # 4. 生成竞对关系矩阵
```

#### 3. 证书有效性验证
```python
def validate_certificate(cert_data):
    # 1. 检查证书有效期
    # 2. 验证认证机构资质
    # 3. 计算证书状态（有效/过期/待续办）
    # 4. 特殊处理技术类证书
```

## 数据质量和性能优化

### 数据质量保障
1. **字段验证** - 严格的数据类型检查
2. **数据去重** - 基于业务键的去重逻辑
3. **异常处理** - 完善的异常数据过滤
4. **数据血缘** - 清晰的数据流转关系

### 性能优化策略
1. **分区策略** - 合理的数据分区设计
2. **缓存机制** - 中间结果持久化
3. **并行计算** - Spark 分布式计算优化
4. **资源管理** - 动态分区数量调整

## 扩展性和维护性

### 模块化设计
- 每个业务模块独立开发和部署
- 统一的接口规范和数据格式
- 可插拔的计算组件

### 配置管理
- 环境相关配置外部化
- 业务规则参数化
- 灵活的作业调度配置

### 监控和运维
- 作业执行状态监控
- 数据质量监控
- 性能指标跟踪

这个项目展现了现代企业级数据仓库的最佳实践，具有良好的架构设计、完善的业务逻辑和强大的扩展能力。

## 项目使用指南

### 环境要求
- **Python**: 3.6+
- **Apache Spark**: 2.4+
- **PySpark**: 对应 Spark 版本
- **依赖库**: pymongo_spark, scikit-learn

### 作业执行方式
每个数据处理模块都可以独立执行，典型的执行命令：

```bash
# 企业风险分析作业
spark-submit \
  --master yarn \
  --deploy-mode cluster \
  tdr/warehouse/dwm/enterprise/dwm_enterprise_risk/builder/spark/dwm_enterprise_risk_compute.py \
  <input_paths> <output_path> <partition_num>

# 推广竞对分析作业
spark-submit \
  --master yarn \
  --deploy-mode cluster \
  tdr/warehouse/dwm/enterprise/dwm_enterprise_promotion_competitor/builder/spark/dwm_enterprise_promotion_competitor_compute.py \
  <input_paths> <output_path> <partition_num>
```

### 数据流转关系
```
ODS层数据 → DWD层清洗 → DWM层计算 → DWS层聚合 → 应用层消费
```

**具体示例 - 推广分析流程：**
1. `ods_promotion` → 原始推广数据
2. `dwd_enterprise` → 清洗后企业数据
3. `dwm_enterprise_promotion_competitor` → 竞对关系计算
4. `dws_enterprise_promotion_stats` → 推广统计汇总

### 配置参数说明
- `input_paths` - 输入数据路径（支持多个路径）
- `output_path` - 输出数据路径
- `partition_num` - 输出分区数量（建议根据数据量调整）

## 开发建议

### 新增业务模块
1. **创建目录结构**：按照标准模板创建 meta、builder、utils 目录
2. **定义数据模型**：在 meta 中定义字段和 Spark Schema
3. **实现计算逻辑**：在 builder/spark 中实现具体的业务逻辑
4. **添加工具函数**：在 utils 中实现模块专用的工具函数

### 代码规范建议
1. **命名规范**：遵循项目既有的命名约定
2. **文档注释**：为每个函数添加详细的文档字符串
3. **错误处理**：添加适当的异常处理逻辑
4. **性能优化**：合理使用 Spark 的缓存和分区机制

### 测试建议
1. **单元测试**：为核心业务逻辑编写单元测试
2. **集成测试**：验证端到端的数据处理流程
3. **性能测试**：在生产规模数据上验证性能表现

## 总结

TDR 项目是一个功能完善、架构清晰的企业数据仓库系统，主要特点包括：

### 优势
1. **架构清晰** - 标准的数据仓库分层架构
2. **模块化设计** - 高内聚、低耦合的模块结构
3. **业务完整** - 覆盖企业数据分析的核心场景
4. **技术先进** - 基于 Spark 的大数据处理能力
5. **扩展性强** - 易于添加新的业务模块

### 应用场景
1. **企业风控** - 全方位的企业风险评估
2. **市场分析** - 竞对识别和市场洞察
3. **合规管理** - 证书资质管理和监控
4. **商业智能** - 企业经营状况分析

### 技术价值
这个项目展示了如何构建一个生产级的数据仓库系统，包含了数据建模、ETL 处理、业务计算、性能优化等各个方面的最佳实践，对于学习大数据技术和数据仓库建设具有很高的参考价值。
