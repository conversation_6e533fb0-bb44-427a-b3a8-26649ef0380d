#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~
    证书表

    Description of this file

    :author: l<PERSON><PERSON><PERSON>
    :copyright: (c) ods_certificate.py, Tungee
    :date created: 2022/12/19
    :python version: 3.6

"""
from tdr.common.constant.common import FormatType


class OdsCertificate(object):
    NAME = 'ods_certificate'

    FORMAT_TYPE = FormatType.bson

    # 字段名
    class Field:
        # https://gitlab.tangees.com/data-collector/enterprise-model/-/blob/master/label-wiki/certificate-dimensions.md
        _id = '_id'
        certAcquireYear = 'certAcquireYear'
        certAcquireYearNum = 'certAcquireYearNum'
        certAgencyContacts = 'certAgencyContacts'
        certAgentInfoList = 'certAgentInfoList'
        certAlterInfoList = 'certAlterInfoList'
        certAmbassadornList = 'certAmbassadornList'
        certApplicant = 'certApplicant'
        certApprovalId = 'certApprovalId'
        certAuthority = 'certAuthority'
        certAuthorityInfoList = 'certAuthorityInfoList'
        certBasicInfoList = 'certBasicInfoList'
        certBulkFoodSell = 'certBulkFoodSell'
        certBusinessAddress = 'certBusinessAddress'
        certBusinessMode = 'certBusinessMode'
        certBusinessPrincipal = 'certBusinessPrincipal'
        certCentralKitchen = 'certCentralKitchen'
        certCollectiveMealDistributionEntities = 'certCollectiveMealDistributionEntities'
        certCommercialActivities = 'certCommercialActivities'
        certDelete = 'certDelete'
        certDetailUpdateTime = 'certDetailUpdateTime'
        certEnterpriseAdministration = 'certEnterpriseAdministration'
        certEnterpriseContacts = 'certEnterpriseContacts'
        certEnterpriseFormerName = 'certEnterpriseFormerName'
        certEnterpriseFranchiseStores = 'certEnterpriseFranchiseStores'
        certEnterpriseIndustry = 'certEnterpriseIndustry'
        certEnterpriseInfoList = 'certEnterpriseInfoList'
        certEnterpriseManagementResources = 'certEnterpriseManagementResources'
        certEnterpriseName = 'certEnterpriseName'
        certEnterpriseOrgCode = 'certEnterpriseOrgCode'
        certEnterprisePrincipal = 'certEnterprisePrincipal'
        certEnterpriseRegNumber = 'certEnterpriseRegNumber'
        certEnterpriseRepresentative = 'certEnterpriseRepresentative'
        certEnterpriseSocialCreditCode = 'certEnterpriseSocialCreditCode'
        certEnterpriseStaff = 'certEnterpriseStaff'
        certEnterpriseTotalAssets = 'certEnterpriseTotalAssets'
        certEnterpriseTotalSales = 'certEnterpriseTotalSales'
        certEnterpriseTxjyInfo = 'certEnterpriseTxjyInfo'
        certEnterpriseType = 'certEnterpriseType'
        certEquipmentModel = 'certEquipmentModel'
        certEquipmentName = 'certEquipmentName'
        certExpireTime = 'certExpireTime'
        certId = 'certId'
        certIdOriginalValue = 'certIdOriginalValue'
        certManufacturer = 'certManufacturer'
        certManufacturerInfoList = 'certManufacturerInfoList'
        certNameFullTime = 'certNameFullTime'
        certNameList = 'certNameList'
        certNetworkBusiness = 'certNetworkBusiness'
        certOrgInfoList = 'certOrgInfoList'
        certOriginalPublishTime = 'certOriginalPublishTime'
        certPrincipalPerson = 'certPrincipalPerson'
        certProduceAddress = 'certProduceAddress'
        certProductInfoList = 'certProductInfoList'
        certProductName = 'certProductName'
        certProductType = 'certProductType'
        certProductTypeAndLabList = 'certProductTypeAndLabList'
        certProducter = 'certProducter'
        certProducterInfoList = 'certProducterInfoList'
        certProductionEnterprise = 'certProductionEnterprise'
        certProjectId = 'certProjectId'
        certPublicationId = 'certPublicationId'
        certPublicationName = 'certPublicationName'
        certPublicationType = 'certPublicationType'
        certPublishTime = 'certPublishTime'
        certRecordAnnual = 'certRecordAnnual'
        certRegion = 'certRegion'
        certRemark = 'certRemark'
        certScope = 'certScope'
        certSelfDeclarationInfoList = 'certSelfDeclarationInfoList'
        certSelfDeclarationOrgInfoList = 'certSelfDeclarationOrgInfoList'
        certSelfDeclarationProductList = 'certSelfDeclarationProductList'
        certSource = 'certSource'
        certSourceV2 = 'certSourceV2'
        certStaffName = 'certStaffName'
        certStaffSource = 'certStaffSource'
        certStaffType = 'certStaffType'
        certStandard = 'certStandard'
        certStatus = 'certStatus'
        certStatusAlterReason = 'certStatusAlterReason'
        certStatusAlterTime = 'certStatusAlterTime'
        certSubitem = 'certSubitem'
        certSupervisionAuthority = 'certSupervisionAuthority'
        certType = 'certType'
        certUrl = 'certUrl'
        cid = 'cid'
        create_time = 'create_time'
        enterpriseSourceId = 'enterpriseSourceId'
        import_update_time = 'import_update_time'
        last_update_time = 'last_update_time'
        mid = 'mid'
        nameId = 'nameId'
        origin_urls = 'origin_urls'
        oss_urls = 'oss_urls'
        update_time = 'update_time'
        certLicenseFiles = 'certLicenseFiles'

    SPARK_SCHEMA = {
        "fields": [
            {
                "metadata": {
                    "comment": "主键：certSource + certId"
                },
                "name": "_id",
                "nullable": False,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "连续公示年度"
                },
                "name": "certAcquireYear",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "max",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "min",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "连续公示年数"
                },
                "name": "certAcquireYearNum",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {
                    "comment": "代理记账企业联系方式（仅用作代理记账企业的联系方式存储）"
                },
                "name": "certAgencyContacts",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "联系方式(加*的联系方式，如：152****8236)"
                                },
                                "name": "contact",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "联系方式类型，具体看标准映射文档"
                                },
                                "name": "contactType",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "认证委托人基本信息列表"
                },
                "name": "certAgentInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书变化历史轨迹列表"
                },
                "name": "certAlterInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "授权代表基本信息列表"
                },
                "name": "certAmbassadornList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书申请人(加密)"
                },
                "name": "certApplicant",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "cn",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "审批文号(辐射安全许可证用)"
                },
                "name": "certApprovalId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "发证机关"
                },
                "name": "certAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "发证机构信息列表"
                },
                "name": "certAuthorityInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书信息列表"
                },
                "name": "certBasicInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "是否散装熟食销售"
                },
                "name": "certBulkFoodSell",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "经营地址"
                },
                "name": "certBusinessAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "经营方式"
                },
                "name": "certBusinessMode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "业务负责人"
                },
                "name": "certBusinessPrincipal",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "是否中央厨房"
                },
                "name": "certCentralKitchen",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "是否集体用餐配送单位"
                },
                "name": "certCollectiveMealDistributionEntities",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "主体业态"
                },
                "name": "certCommercialActivities",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书删除状态 `只允许为[删除，未删除]`"
                },
                "name": "certDelete",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "详情页爬取成功时间(date_str)"
                },
                "name": "certDetailUpdateTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书企业所属管理机构"
                },
                "name": "certEnterpriseAdministration",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书企业联系方式"
                },
                "name": "certEnterpriseContacts",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "联系方式"
                                },
                                "name": "contact",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "联系人姓名"
                                },
                                "name": "contactName",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "contactName 加星前的 md5 值"
                                },
                                "name": "contactNameMd5",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "contactPageImage",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "来源URL"
                                },
                                "name": "contactPageLink",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "联系方式类型，具体看标准映射文档"
                                },
                                "name": "contactType",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业曾用名 加密"
                },
                "name": "certEnterpriseFormerName",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "cn",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "境内加盟店"
                },
                "name": "certEnterpriseFranchiseStores",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "城市"
                                },
                                "name": "city",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "数量"
                                },
                                "name": "num",
                                "nullable": True,
                                "type": "long"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业所属行业"
                },
                "name": "certEnterpriseIndustry",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书企业基本信息列表/基本信息列表"
                },
                "name": "certEnterpriseInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "经营资源信息"
                },
                "name": "certEnterpriseManagementResources",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "权利号"
                                },
                                "name": "id",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "权利性质"
                                },
                                "name": "nature",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "权利类型"
                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "权利日期"
                                },
                                "name": "begin",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "特许品牌"
                                },
                                "name": "brand",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "权利期限"
                                },
                                "name": "end",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "注册类别"
                                },
                                "name": "registerCategory",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业名(加密)"
                },
                "name": "certEnterpriseName",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "cn",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "en",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "企业表唯一标识"
                                },
                                "name": "nameId",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "nameIdSource",
                                "nullable": True,
                                "type": {
                                    "fields": [
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "name",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "nameId",
                                            "nullable": True,
                                            "type": "string"
                                        },
                                        {
                                            "metadata": {
                                                "comment": ""
                                            },
                                            "name": "source",
                                            "nullable": True,
                                            "type": "string"
                                        }
                                    ],
                                    "type": "struct"
                                }
                            },
                            {
                                "metadata": {
                                    "comment": "游戏证书用：出版单位/运营单位"
                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业机构代码"
                },
                "name": "certEnterpriseOrgCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "机构负责人"
                },
                "name": "certEnterprisePrincipal",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业营业执照编号"
                },
                "name": "certEnterpriseRegNumber",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书企业法人代表"
                },
                "name": "certEnterpriseRepresentative",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书企业信用代码"
                },
                "name": "certEnterpriseSocialCreditCode",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书企业人数"
                },
                "name": "certEnterpriseStaff",
                "nullable": True,
                "type": "long"
            },
            {
                "metadata": {
                    "comment": "证书企业资产总额"
                },
                "name": "certEnterpriseTotalAssets",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书企业销售总额"
                },
                "name": "certEnterpriseTotalSales",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "商业特许经营备案信息"
                },
                "name": "certEnterpriseTxjyInfo",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": "备案号"
                            },
                            "name": "id",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "备案公告时间"
                            },
                            "name": "time",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "证书企业类别(安全生产许可证用)"
                },
                "name": "certEnterpriseType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "设备型号(电信设备进网许可证用)"
                },
                "name": "certEquipmentModel",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "设备名称(电信设备进网许可证用)"
                },
                "name": "certEquipmentName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书有效期(date_str) `注意：字段值存在为空的情况`"
                },
                "name": "certExpireTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书编号"
                },
                "name": "certId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书编号原始值"
                },
                "name": "certIdOriginalValue",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "制造商(加密)"
                },
                "name": "certManufacturer",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "cn",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "en",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "制造商基本信息列表"
                },
                "name": "certManufacturerInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "企业证书资质采全时间(date_str) `用于四库源`"
                },
                "name": "certNameFullTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "资质名列表"
                },
                "name": "certNameList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "是否网络经营"
                },
                "name": "certNetworkBusiness",
                "nullable": True,
                "type": "boolean"
            },
            {
                "metadata": {
                    "comment": "获证组织基本信息列表"
                },
                "name": "certOrgInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "首次发证日期(date_str)"
                },
                "name": "certOriginalPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "certPrincipalPerson",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "生产地址"
                },
                "name": "certProduceAddress",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "生产厂 加密"
                },
                "name": "certProducter",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "cn",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "en",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": "企业表唯一标识"
                            },
                            "name": "nameId",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "nameIdSource",
                            "nullable": True,
                            "type": {
                                "fields": [
                                    {
                                        "metadata": {
                                            "comment": ""
                                        },
                                        "name": "name",
                                        "nullable": True,
                                        "type": "string"
                                    },
                                    {
                                        "metadata": {
                                            "comment": ""
                                        },
                                        "name": "nameId",
                                        "nullable": True,
                                        "type": "string"
                                    },
                                    {
                                        "metadata": {
                                            "comment": ""
                                        },
                                        "name": "source",
                                        "nullable": True,
                                        "type": "string"
                                    }
                                ],
                                "type": "struct"
                            }
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "生产企业基本信息列表"
                },
                "name": "certProducterInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "产品信息列表 (废弃，无解析使用)"
                },
                "name": "certProductInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "生产企业(电信设备进网许可证用)"
                },
                "name": "certProductionEnterprise",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "产品名称"
                },
                "name": "certProductName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "产品类别/规格"
                },
                "name": "certProductType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "产品类型及实验室信息列表"
                },
                "name": "certProductTypeAndLabList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "项目编号(辐射安全许可证用)"
                },
                "name": "certProjectId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "出版物号"
                },
                "name": "certPublicationId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "出版物名称"
                },
                "name": "certPublicationName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "出版物类别"
                },
                "name": "certPublicationType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "发证日期(date_str) `注意：字段值存在为空的情况`"
                },
                "name": "certPublishTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "代理记账证书备案年度"
                },
                "name": "certRecordAnnual",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书地区"
                },
                "name": "certRegion",
                "nullable": True,
                "type": {
                    "fields": [
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "city",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "district",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "name",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "province",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "subdistrict",
                            "nullable": True,
                            "type": "string"
                        },
                        {
                            "metadata": {
                                "comment": ""
                            },
                            "name": "value",
                            "nullable": True,
                            "type": "string"
                        }
                    ],
                    "type": "struct"
                }
            },
            {
                "metadata": {
                    "comment": "资质备注"
                },
                "name": "certRemark",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "资质范围"
                },
                "name": "certScope",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "自我声明信息列表"
                },
                "name": "certSelfDeclarationInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "自我声明组织基本信息列表"
                },
                "name": "certSelfDeclarationOrgInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "key",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": ""
                                },
                                "name": "value",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "自我声明产品列表"
                },
                "name": "certSelfDeclarationProductList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "[`证书来源`](https://gitlab.tangees.com/data-collector/tungee-parser/-/wikis/certificate-dimension#%E9%99%84%E5%BD%95)"
                },
                "name": "certSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "certSourceV2",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "certStaffName",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "certStaffSource",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "certStaffType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "产品标准"
                },
                "name": "certStandard",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书状态"
                },
                "name": "certStatus",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书状态变化原因"
                },
                "name": "certStatusAlterReason",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书状态变化时间"
                },
                "name": "certStatusAlterTime",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "certSubitem",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "日常监督管理机构"
                },
                "name": "certSupervisionAuthority",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "资质类别"
                },
                "name": "certType",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书落地页"
                },
                "name": "certUrl",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": "证书整改信息列表"
                },
                "name": "certRectificationInfoList",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "containsNull": True,
                        "elementType": {
                            "fields": [
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "key",
                                    "nullable": True,
                                    "type": "string"
                                },
                                {
                                    "metadata": {
                                        "comment": ""
                                    },
                                    "name": "value",
                                    "nullable": True,
                                    "type": "string"
                                }
                            ],
                            "type": "struct"
                        },
                        "type": "array"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "许可证文件列表"
                },
                "name": "certLicenseFiles",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "许可证文件名称"
                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "许可证文件原始链接"
                                },
                                "name": "link",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "许可证文件oss"
                                },
                                "name": "url",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": "许可证附件列表"
                },
                "name": "certAttachments",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": {
                        "fields": [
                            {
                                "metadata": {
                                    "comment": "附件名称"
                                },
                                "name": "name",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "附件类型"
                                },
                                "name": "type",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "附件描述"
                                },
                                "name": "description",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "附件原始链接"
                                },
                                "name": "link",
                                "nullable": True,
                                "type": "string"
                            },
                            {
                                "metadata": {
                                    "comment": "附件oss"
                                },
                                "name": "url",
                                "nullable": True,
                                "type": "string"
                            }
                        ],
                        "type": "struct"
                    },
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "cid",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "create_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "enterpriseSourceId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "import_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "last_update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "nameId",
                "nullable": True,
                "type": "string"
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "origin_urls",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "oss_urls",
                "nullable": True,
                "type": {
                    "containsNull": True,
                    "elementType": "string",
                    "type": "array"
                }
            },
            {
                "metadata": {
                    "comment": ""
                },
                "name": "update_time",
                "nullable": True,
                "type": "timestamp"
            },
            {
                "metadata": {
                    "comment": "证书评定等级"
                },
                "name": "certEvaluationLevel",
                "nullable": True,
                "type": "string"
            }
        ],
        "type": "struct"
    }
