# dbt_project.yml

# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: 'tdr'
version: '1.0.0'
config-version: 2

# This setting configures which "profile" dbt uses for this project.
profile: 'tdr'

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"

# Configuring models
# Full documentation: https://docs.getdbt.com/reference/model-configs

models:
  tdr:
    # Config indicated by + and applies to all files under models/...
    +materialized: table
    +file_format: delta  # Use Delta Lake format for better performance
    
    # ODS layer - staging models
    staging:
      +materialized: view
      +schema: ods
      
    # DWD layer - cleaned and standardized data
    intermediate:
      +materialized: table
      +schema: dwd
      +file_format: delta
      +partition_by: ["dt"]  # Partition by date for better performance
      
    # DWM layer - business logic models
    marts:
      +materialized: table
      +schema: dwm
      +file_format: delta
      +partition_by: ["dt"]
      
      # Enterprise domain
      enterprise:
        +tags: ["enterprise", "core"]
        
        # Risk analysis models
        risk:
          +tags: ["risk", "compliance"]
          
        # Promotion analysis models  
        promotion:
          +tags: ["promotion", "marketing"]
          
        # Certificate analysis models
        certificate:
          +tags: ["certificate", "qualification"]
    
    # DWS layer - data service models
    data_marts:
      +materialized: table
      +schema: dws
      +file_format: delta
      +partition_by: ["dt"]
      +tags: ["data_service", "analytics"]

# Global configurations
vars:
  # Date variables for incremental processing
  start_date: '2020-01-01'
  end_date: '{{ run_started_at.strftime("%Y-%m-%d") }}'
  
  # Business logic variables
  risk_score_weights:
    anomaly: 0.2
    punishment: 0.3
    enforcement: 0.25
    dishonesty: 0.15
    bankruptcy: 0.1
    
  # Performance tuning variables
  default_partition_count: 200
  large_table_partition_count: 500

# Seeds configuration
seeds:
  tdr:
    +file_format: delta
    +schema: seeds
    
    # Reference data
    reference:
      +tags: ["reference", "lookup"]

# Snapshots configuration  
snapshots:
  tdr:
    +target_schema: snapshots
    +strategy: timestamp
    +updated_at: update_time

# Tests configuration
tests:
  +store_failures: true
  +schema: test_failures

# Documentation
docs:
  +node_color: "#8B4513"  # Brown color for TDR project
