-- models/intermediate/int_enterprise_clean.sql
-- Intermediate model for cleaned enterprise data
-- Equivalent to dwd_enterprise in the original system

{{ config(
    materialized='table',
    tags=['intermediate', 'enterprise', 'core'],
    partition_by=['dt']
) }}

with enterprise_data as (
    select * from {{ ref('stg_enterprise') }}
),

cleaned_enterprise as (
    select
        _id,
        name,
        socialCreditCode,
        orgCode,
        legalRepresentative,
        foundTime,
        address,
        businessScope,
        registeredCapital,
        businessType,
        registrationAuthority,
        approvalDate,
        enterpriseStatus,
        operStatus,
        
        -- Generate standardized nameId for linking
        {{ generate_md5('name', 16) }} as nameId,
        
        -- Complex structured fields
        holderList,
        memberList,
        branchOfficeList,
        alterationList,
        anomalyList,
        punishmentList,
        
        -- Business fields
        industry,
        homepage,
        ICPList,
        location,
        
        -- Derived fields
        case 
            when operStatus in ('在业', '存续', '正常') then 1
            else 0
        end as isActive,
        
        case
            when anomalyList is not null and size(anomalyList) > 0 then 1
            else 0
        end as isAnomaly,
        
        case
            when punishmentList is not null and size(punishmentList) > 0 then 1
            else 0
        end as hasPunishment,
        
        -- Extract year from foundTime for analysis
        {{ extract_year('foundTime') }} as foundYear,
        
        -- Metadata
        create_time,
        update_time,
        dt
        
    from enterprise_data
    where name is not null
      and trim(name) != ''
)

select * from cleaned_enterprise
