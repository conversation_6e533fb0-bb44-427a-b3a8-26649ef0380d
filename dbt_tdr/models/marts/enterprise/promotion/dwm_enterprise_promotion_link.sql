-- models/marts/enterprise/promotion/dwm_enterprise_promotion_link.sql
-- DWM model for enterprise promotion link analysis

{{ config(
    materialized='table',
    tags=['dwm', 'enterprise', 'promotion', 'link'],
    partition_by=['dt']
) }}

with promotion_data as (
    select * from {{ ref('stg_promotion') }}
),

-- Aggregate promotion data by enterprise
promotion_aggregated as (
    select
        nameId as _id,  -- Use nameId as primary key
        nameId,
        
        -- Get the latest promotion time
        max(prmtTime) as lastPrmtTime,
        
        -- Collect all unique promotion keywords
        {{ array_remove_nulls('collect_set(prmtKey)') }} as prmtKeys,
        
        -- Count unique promotion keywords
        count(distinct prmtKey) as prmtKeysNumber,
        
        -- Get the primary promotion link (most recent)
        first_value(prmtLink) over (
            partition by nameId 
            order by prmtTime desc
            rows between unbounded preceding and unbounded following
        ) as prmtLink,
        
        -- Add partition column
        {{ get_current_date_partition() }} as dt
        
    from promotion_data
    where nameId is not null
      and prmt<PERSON><PERSON> is not null
    group by nameId
)

select
    _id,
    nameId,
    lastPrmtTime,
    prmtKeys,
    prmtKeysNumber,
    prmtLink,
    dt
from promotion_aggregated
