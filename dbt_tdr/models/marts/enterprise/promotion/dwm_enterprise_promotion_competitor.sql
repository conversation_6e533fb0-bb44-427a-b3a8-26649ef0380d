-- models/marts/enterprise/promotion/dwm_enterprise_promotion_competitor.sql
-- DWM model for enterprise promotion competitor analysis
-- Replaces the original PySpark RDD job with SQL

{{ config(
    materialized='table',
    tags=['dwm', 'enterprise', 'promotion', 'competitor'],
    partition_by=['dt']
) }}

with promotion_data as (
    select * from {{ ref('stg_promotion') }}
),

enterprise_data as (
    select 
        nameId,
        name,
        address
    from {{ ref('int_enterprise_clean') }}
),

-- Step 1: Group promotions by keyword to find competitors
keyword_groups as (
    select
        prmtKey,
        collect_list(
            struct(
                nameId,
                prmtEnterpriseName as name,
                prmtTime,
                prmtSource
            )
        ) as enterprises
    from promotion_data
    where prmtKey is not null
      and nameId is not null
    group by prmtKey
    having size(collect_list(nameId)) > 1  -- Only keywords with multiple enterprises
),

-- Step 2: Generate competitor pairs from each keyword group
competitor_pairs as (
    select
        prmtKey,
        enterprise1.nameId as nameId,
        enterprise1.name as enterpriseName,
        enterprise2.nameId as competitorId,
        enterprise2.name as competitorName,
        array(prmtKey) as prmtKeys,
        1 as prmtSameKeysNumber
    from keyword_groups
    lateral view explode(enterprises) t1 as enterprise1
    lateral view explode(enterprises) t2 as enterprise2
    where enterprise1.nameId != enterprise2.nameId  -- Exclude self-comparison
),

-- Step 3: Aggregate competitor relationships
competitor_aggregated as (
    select
        nameId,
        competitorId,
        max(competitorName) as competitorName,
        {{ array_remove_nulls('collect_set(prmtKeys[0])') }} as prmtKeys,
        count(distinct prmtKeys[0]) as prmtSameKeysNumber
    from competitor_pairs
    group by nameId, competitorId
),

-- Step 4: Add enterprise address information
final_competitors as (
    select
        {{ generate_md5('concat(ca.nameId, "_", ca.competitorId)', 16) }} as _id,
        ca.nameId,
        ca.competitorId,
        ca.competitorName,
        ca.prmtKeys,
        ca.prmtSameKeysNumber,
        
        -- Add competitor address from enterprise data
        e.address,
        
        -- Add partition column
        {{ get_current_date_partition() }} as dt
        
    from competitor_aggregated ca
    left join enterprise_data e
        on ca.competitorId = e.nameId
),

-- Step 5: Limit to top 1000 competitors per enterprise (performance optimization)
ranked_competitors as (
    select
        *,
        row_number() over (
            partition by nameId 
            order by prmtSameKeysNumber desc, competitorId
        ) as rn
    from final_competitors
)

select
    _id,
    nameId,
    competitorId,
    competitorName,
    prmtKeys,
    prmtSameKeysNumber,
    address,
    dt
from ranked_competitors
where rn <= 1000  -- Limit to top 1000 competitors per enterprise
