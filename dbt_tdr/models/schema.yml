# models/schema.yml
# Global schema definitions and documentation

version: 2

# Source definitions for ODS tables
sources:
  - name: ods
    description: "Operational Data Store - Raw business data"
    schema: ods
    tables:
      - name: ods_enterprise
        description: "Enterprise basic information (core table with 6000+ fields)"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: name
            description: "Enterprise name"
            tests:
              - not_null
          - name: socialCreditCode
            description: "Unified social credit code"
          - name: orgCode
            description: "Organization code"
          - name: legalRepresentative
            description: "Legal representative"
          - name: foundTime
            description: "Establishment time"
          - name: address
            description: "Registered address"
          - name: businessScope
            description: "Business scope"
          - name: registeredCapital
            description: "Registered capital"
          - name: create_time
            description: "Record creation time"
          - name: update_time
            description: "Record update time"
            
      - name: ods_promotion
        description: "Enterprise promotion and marketing data"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: prmtEnterpriseName
            description: "Promotion enterprise name"
          - name: prmtKey
            description: "Promotion keyword"
          - name: prmtText
            description: "Promotion text content"
          - name: prmtLink
            description: "Promotion link"
          - name: prmtSource
            description: "Promotion source"
          - name: prmtTime
            description: "Promotion time"
            
      - name: ods_judgment_document
        description: "Court judgment documents"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: jdCaseNumber
            description: "Case number"
          - name: jdTitle
            description: "Document title"
          - name: jdPublishDate
            description: "Publish date"
          - name: jdCourt
            description: "Court name"
          - name: jdCaseType
            description: "Case type"
            
      - name: ods_court_announcement
        description: "Court announcements"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: caTitle
            description: "Announcement title"
          - name: caPublishDate
            description: "Publish date"
          - name: caCourt
            description: "Court name"
          - name: caType
            description: "Announcement type"
            
      - name: ods_enterprise_anomaly
        description: "Enterprise anomaly information"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: nameId
            description: "Enterprise name ID"
          - name: createReason
            description: "Anomaly creation reason"
          - name: createDate
            description: "Anomaly creation date"
          - name: removeDate
            description: "Anomaly removal date"
            
      - name: ods_enforcement
        description: "Enforcement information"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: efCaseNumber
            description: "Enforcement case number"
          - name: efEnterpriseName
            description: "Enforced enterprise name"
          - name: efExecutiveCourt
            description: "Executive court"
          - name: efCaseCreateTime
            description: "Case creation time"
          - name: efSubjectMoney
            description: "Enforcement amount"
            
      - name: ods_certificate
        description: "Certificate information"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: certName
            description: "Certificate name"
          - name: certType
            description: "Certificate type"
          - name: certPublishTime
            description: "Certificate publish time"
          - name: certExpireTime
            description: "Certificate expire time"
            
      - name: ods_enterprise_certificate
        description: "Enterprise certificate relationship"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: nameId
            description: "Enterprise name ID"
          - name: certId
            description: "Certificate ID"
          - name: certStatus
            description: "Certificate status"
            
      - name: ods_certification_authority
        description: "Certification authority information"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: caName
            description: "Authority name"
          - name: caType
            description: "Authority type"
          - name: caStatus
            description: "Authority status"
            
      - name: ods_enterprise_annual_qualification
        description: "Enterprise annual qualification"
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: nameId
            description: "Enterprise name ID"
          - name: qualificationType
            description: "Qualification type"
          - name: qualificationYear
            description: "Qualification year"
            
      - name: ods_combined_punishment
        description: "Combined punishment information"
        
      - name: ods_bankruptcy_reorganization
        description: "Bankruptcy reorganization information"
        
      - name: ods_punish_dishonesty
        description: "Dishonesty punishment information"
        
      - name: ods_punishment
        description: "Administrative punishment information"
