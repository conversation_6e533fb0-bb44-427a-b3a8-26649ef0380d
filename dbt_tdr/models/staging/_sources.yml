# models/staging/_sources.yml
# Source table definitions for staging layer

version: 2

sources:
  - name: raw_ods
    description: "Raw ODS tables from the original TDR system"
    schema: ods
    
    # Freshness checks for critical tables
    freshness:
      warn_after: {count: 12, period: hour}
      error_after: {count: 24, period: hour}
    
    # Loaded at field for freshness checks
    loaded_at_field: update_time
    
    tables:
      # Enterprise core table
      - name: ods_enterprise
        description: "Enterprise basic information - core table with 6000+ fields"
        identifier: ods_enterprise
        freshness:
          warn_after: {count: 6, period: hour}
          error_after: {count: 12, period: hour}
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: name
            description: "Enterprise name"
            tests:
              - not_null
          - name: socialCreditCode
            description: "Unified social credit code"
          - name: nameId
            description: "Enterprise name ID for linking"
          - name: update_time
            description: "Last update timestamp"
            
      # Promotion data
      - name: ods_promotion
        description: "Enterprise promotion and marketing data"
        identifier: ods_promotion
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: prmtEnterpriseName
            description: "Promotion enterprise name"
          - name: prmtKey
            description: "Promotion keyword"
          - name: prmtTime
            description: "Promotion time"
          - name: update_time
            description: "Last update timestamp"
            
      # Judicial documents
      - name: ods_judgment_document
        description: "Court judgment documents"
        identifier: ods_judgment_document
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: jdCaseNumber
            description: "Case number"
          - name: jdPublishDate
            description: "Document publish date"
          - name: update_time
            description: "Last update timestamp"
            
      # Court announcements
      - name: ods_court_announcement
        description: "Court announcements"
        identifier: ods_court_announcement
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: caPublishDate
            description: "Announcement publish date"
          - name: update_time
            description: "Last update timestamp"
            
      # Enterprise anomaly
      - name: ods_enterprise_anomaly
        description: "Enterprise anomaly information"
        identifier: ods_enterprise_anomaly
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: nameId
            description: "Enterprise name ID"
          - name: createDate
            description: "Anomaly creation date"
          - name: update_time
            description: "Last update timestamp"
            
      # Enforcement information
      - name: ods_enforcement
        description: "Enforcement information"
        identifier: ods_enforcement
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: efCaseNumber
            description: "Enforcement case number"
          - name: efCaseCreateTime
            description: "Case creation time"
          - name: update_time
            description: "Last update timestamp"
            
      # Certificate information
      - name: ods_certificate
        description: "Certificate information"
        identifier: ods_certificate
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: certPublishTime
            description: "Certificate publish time"
          - name: update_time
            description: "Last update timestamp"
            
      # Enterprise certificate relationship
      - name: ods_enterprise_certificate
        description: "Enterprise certificate relationship"
        identifier: ods_enterprise_certificate
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: nameId
            description: "Enterprise name ID"
          - name: certId
            description: "Certificate ID"
          - name: update_time
            description: "Last update timestamp"
            
      # Certification authority
      - name: ods_certification_authority
        description: "Certification authority information"
        identifier: ods_certification_authority
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: update_time
            description: "Last update timestamp"
            
      # Enterprise annual qualification
      - name: ods_enterprise_annual_qualification
        description: "Enterprise annual qualification"
        identifier: ods_enterprise_annual_qualification
        columns:
          - name: _id
            description: "Primary key"
            tests:
              - unique
              - not_null
          - name: nameId
            description: "Enterprise name ID"
          - name: update_time
            description: "Last update timestamp"
            
      # Other ODS tables
      - name: ods_combined_punishment
        description: "Combined punishment information"
        identifier: ods_combined_punishment
        
      - name: ods_bankruptcy_reorganization
        description: "Bankruptcy reorganization information"
        identifier: ods_bankruptcy_reorganization
        
      - name: ods_punish_dishonesty
        description: "Dishonesty punishment information"
        identifier: ods_punish_dishonesty
        
      - name: ods_punishment
        description: "Administrative punishment information"
        identifier: ods_punishment
