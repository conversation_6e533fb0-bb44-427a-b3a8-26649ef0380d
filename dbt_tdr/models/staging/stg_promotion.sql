-- models/staging/stg_promotion.sql
-- Staging model for promotion data

{{ config(
    materialized='view',
    tags=['staging', 'promotion', 'marketing']
) }}

with source_data as (
    select * from {{ source('raw_ods', 'ods_promotion') }}
),

cleaned_data as (
    select
        _id,
        {{ standardize_enterprise_name('prmtEnterpriseName') }} as prmtEnterpriseName,
        trim(prmtKey) as prmtKey,
        prmtText,
        prmtLink,
        prmtSource,
        {{ convert_date_format('prmtTime') }} as prmtTime,
        
        -- Generate nameId for linking (equivalent to original logic)
        {{ generate_md5('prmtEnterpriseName', 16) }} as nameId,
        
        -- Metadata fields
        create_time,
        update_time,
        {{ add_partition_column('update_time') }}
        
    from source_data
    where {{ is_valid_string('prmtEnterpriseName') }}
      and {{ is_valid_string('prmtKey') }}
      and _id is not null
)

select * from cleaned_data

{{ incremental_where_clause('update_time') }}
