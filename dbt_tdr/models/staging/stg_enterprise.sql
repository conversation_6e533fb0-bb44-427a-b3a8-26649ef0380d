-- models/staging/stg_enterprise.sql
-- Staging model for enterprise data
-- Equivalent to the original ods_enterprise table but with basic cleaning

{{ config(
    materialized='view',
    tags=['staging', 'enterprise', 'core']
) }}

with source_data as (
    select * from {{ source('raw_ods', 'ods_enterprise') }}
),

cleaned_data as (
    select
        _id,
        {{ standardize_enterprise_name('name') }} as name,
        socialCreditCode,
        orgCode,
        legalRepresentative,
        {{ convert_date_format('foundTime') }} as foundTime,
        address,
        businessScope,
        registeredCapital,
        businessType,
        registrationAuthority,
        {{ convert_date_format('approvalDate') }} as approvalDate,
        enterpriseStatus,
        operStatus,
        
        -- Complex fields (keep as-is for now)
        holderList,
        memberList,
        branchOfficeList,
        alterationList,
        anomalyList,
        punishmentList,
        
        -- Additional fields
        industry,
        homepage,
        ICPList,
        location,
        
        -- Metadata fields
        create_time,
        update_time,
        {{ add_partition_column('update_time') }}
        
    from source_data
    where {{ is_valid_string('name') }}
      and _id is not null
)

select * from cleaned_data

-- Add incremental logic for future optimization
{{ incremental_where_clause('update_time') }}
