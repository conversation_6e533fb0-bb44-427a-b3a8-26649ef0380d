-- models/data_marts/dws_enterprise_promotion_stats.sql
-- DWS model for enterprise promotion statistics
-- Final data service layer for promotion analytics

{{ config(
    materialized='table',
    tags=['dws', 'enterprise', 'promotion', 'analytics'],
    partition_by=['dt']
) }}

with promotion_data as (
    select * from {{ ref('stg_promotion') }}
),

competitor_data as (
    select * from {{ ref('dwm_enterprise_promotion_competitor') }}
),

-- Base promotion statistics
promotion_stats as (
    select
        nameId as _id,
        nameId,
        
        -- Basic promotion indicators
        1 as hasPrmt,  -- Has promotion if record exists
        max(prmtTime) as lastPrmtTime,
        
        -- Promotion keywords analysis
        {{ array_remove_nulls('collect_set(prmtKey)') }} as prmtKeys,
        count(distinct prmtKey) as prmtKeysNumber,
        
        -- Recent month keywords (last 30 days)
        {{ array_remove_nulls('collect_set(case when datediff(current_date(), prmtTime) <= 30 then prmtKey end)') }} as prmtKeyLastMonth,
        
        -- Promotion links
        {{ array_remove_nulls('collect_set(prmtLink)') }} as prmtLinks,
        count(distinct prmtLink) as prmtLinksNumber,
        
        -- Promotion sources
        {{ array_remove_nulls('collect_set(prmtSource)') }} as prmtSources,
        
        -- Promotion text content
        {{ array_remove_nulls('collect_set(prmtText)') }} as prmtText
        
    from promotion_data
    where nameId is not null
    group by nameId
),

-- Competitor statistics
competitor_stats as (
    select
        nameId,
        count(distinct competitorId) as prmtCompetitorsNumber
    from competitor_data
    group by nameId
),

-- Combine all statistics
final_stats as (
    select
        ps._id,
        ps.nameId,
        ps.hasPrmt,
        ps.lastPrmtTime,
        coalesce(cs.prmtCompetitorsNumber, 0) as prmtCompetitorsNumber,
        ps.prmtKeyLastMonth,
        ps.prmtKeys,
        ps.prmtKeysNumber,
        ps.prmtLinks,
        ps.prmtLinksNumber,
        ps.prmtSources,
        ps.prmtText,
        
        -- Tab statistics for UI display
        struct(
            ps.prmtKeysNumber as `operating_info::promotion`
        ) as tabStats,
        
        -- Add partition column
        {{ get_current_date_partition() }} as dt
        
    from promotion_stats ps
    left join competitor_stats cs
        on ps.nameId = cs.nameId
)

select * from final_stats
