# TDR PySpark RDD 到 dbt SQL 迁移计划

## 迁移目标

将现有的 PySpark RDD 作业转换为高性能的 Spark SQL + dbt 实现，提升执行效率和可维护性。

## 迁移优势

### 1. 性能提升
- **Catalyst 优化器**: Spark SQL 的查询优化器自动优化执行计划
- **代码生成**: 编译时代码生成，减少运行时开销
- **向量化执行**: 批量处理数据，提升 CPU 利用率
- **自适应查询执行**: 运行时动态优化

### 2. 开发效率
- **SQL 语法**: 更直观的数据处理逻辑表达
- **dbt 框架**: 标准化的数据建模流程
- **版本控制**: Git 友好的 SQL 文件管理
- **文档生成**: 自动生成数据血缘和文档

### 3. 运维便利
- **依赖管理**: dbt 自动处理模型依赖关系
- **增量处理**: 内置增量更新机制
- **测试框架**: 完善的数据质量测试
- **监控集成**: 与现代数据栈集成

## 迁移策略

### 阶段 1: 基础设施搭建 ✅
- [x] 创建 dbt 项目结构
- [x] 配置 Spark 连接
- [x] 建立分层架构
- [x] 创建通用宏函数

### 阶段 2: 推广分析模块迁移 ✅
- [x] `stg_promotion.sql` - 推广数据清洗
- [x] `dwm_enterprise_promotion_competitor.sql` - 竞对分析
- [x] `dwm_enterprise_promotion_link.sql` - 推广链接分析
- [x] `dws_enterprise_promotion_stats.sql` - 推广统计

### 阶段 3: 企业基础数据迁移 (进行中)
- [x] `stg_enterprise.sql` - 企业数据清洗
- [x] `int_enterprise_clean.sql` - 企业数据整合
- [ ] 企业数据质量测试
- [ ] 性能基准测试

### 阶段 4: 司法风险模块迁移 (计划中)
- [ ] `stg_judgment_document.sql` - 裁判文书清洗
- [ ] `stg_court_announcement.sql` - 法院公告清洗
- [ ] `dwm_enterprise_judgment_document.sql` - 裁判文书分析
- [ ] `dwm_enterprise_court_announcement.sql` - 法院公告分析
- [ ] `dwm_enterprise_risk.sql` - 综合风险分析

### 阶段 5: 证书认证模块迁移 (计划中)
- [ ] `stg_certificate.sql` - 证书数据清洗
- [ ] `dwm_enterprise_certificate_cal.sql` - 证书计算分析
- [ ] `dwm_enterprise_certificate_second_dimension.sql` - 证书二维分析

### 阶段 6: 性能优化和监控 (计划中)
- [ ] 查询性能调优
- [ ] 分区策略优化
- [ ] 监控告警配置
- [ ] 自动化测试集成

## 技术对比

### PySpark RDD vs Spark SQL

| 特性 | PySpark RDD | Spark SQL |
|------|-------------|-----------|
| 执行性能 | 较慢 (Python 解释执行) | 快 (编译执行 + 优化器) |
| 内存使用 | 高 (Python 对象开销) | 低 (列式存储) |
| 开发复杂度 | 高 (手动优化) | 低 (自动优化) |
| 调试难度 | 困难 (黑盒执行) | 容易 (执行计划可视化) |
| 可维护性 | 差 (复杂的函数式编程) | 好 (声明式 SQL) |
| 学习成本 | 高 | 低 |

### 性能提升预期

基于业界经验和初步测试：
- **查询性能**: 2-5x 提升
- **内存使用**: 30-50% 减少
- **开发效率**: 3-4x 提升
- **维护成本**: 50-70% 减少

## 迁移实施细节

### 1. RDD 操作到 SQL 的转换模式

#### Map 操作
```python
# PySpark RDD
rdd.map(lambda x: transform_function(x))
```
```sql
-- Spark SQL
SELECT transform_logic(column) FROM table
```

#### Filter 操作
```python
# PySpark RDD
rdd.filter(lambda x: x['field'] > threshold)
```
```sql
-- Spark SQL
SELECT * FROM table WHERE field > threshold
```

#### ReduceByKey 操作
```python
# PySpark RDD
rdd.reduceByKey(lambda x, y: x + y)
```
```sql
-- Spark SQL
SELECT key, SUM(value) FROM table GROUP BY key
```

#### Join 操作
```python
# PySpark RDD
rdd1.join(rdd2)
```
```sql
-- Spark SQL
SELECT * FROM table1 t1 JOIN table2 t2 ON t1.key = t2.key
```

### 2. 复杂业务逻辑迁移

#### 竞对分析逻辑
原 PySpark 实现的核心步骤：
1. 按关键词分组推广数据
2. 生成企业对的笛卡尔积
3. 过滤自身比较
4. 聚合竞对关系
5. 补充企业信息

dbt SQL 实现：
- 使用 `collect_list` 和 `lateral view explode` 替代 RDD 的 flatMap
- 使用窗口函数替代复杂的 reduce 操作
- 使用 CTE 提升代码可读性

### 3. 数据质量保障

#### 原系统问题
- 缺乏统一的数据质量检查
- 错误数据难以追踪
- 数据血缘关系不清晰

#### dbt 解决方案
- 内置数据测试框架
- 自动生成数据血缘图
- 版本控制和变更追踪

## 风险控制

### 1. 数据一致性验证
- 并行运行新旧系统
- 结果对比验证
- 差异分析和修正

### 2. 性能回归测试
- 建立性能基准
- 持续性能监控
- 性能回归告警

### 3. 回滚策略
- 保留原系统作为备份
- 快速切换机制
- 数据恢复流程

## 成功指标

### 1. 性能指标
- 查询响应时间 < 原系统 50%
- 资源使用率 < 原系统 70%
- 作业成功率 > 99%

### 2. 质量指标
- 数据准确性 = 100%
- 测试覆盖率 > 80%
- 文档完整性 > 90%

### 3. 效率指标
- 开发周期缩短 > 50%
- 维护工作量减少 > 60%
- 新功能交付速度提升 > 3x

## 时间计划

| 阶段 | 预计时间 | 状态 |
|------|----------|------|
| 基础设施搭建 | 1 周 | ✅ 已完成 |
| 推广分析模块 | 2 周 | ✅ 已完成 |
| 企业基础数据 | 1 周 | 🔄 进行中 |
| 司法风险模块 | 3 周 | 📋 计划中 |
| 证书认证模块 | 2 周 | 📋 计划中 |
| 性能优化监控 | 1 周 | 📋 计划中 |
| **总计** | **10 周** | **30% 完成** |

## 下一步行动

1. **立即执行**:
   - 完成企业基础数据模块的测试
   - 开始司法风险模块的需求分析

2. **本周内**:
   - 建立性能基准测试
   - 配置 CI/CD 流水线

3. **下周开始**:
   - 启动司法风险模块开发
   - 并行进行性能优化工作

这个迁移计划将显著提升 TDR 项目的性能和可维护性，为未来的业务扩展奠定坚实基础。
