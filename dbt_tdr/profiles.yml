# profiles.yml
# This file should be placed in ~/.dbt/ directory or specified via --profiles-dir

tdr:
  target: dev
  outputs:
    dev:
      type: spark
      method: session  # Use existing Spark session
      schema: tdr_dev
      host: localhost
      port: 10000
      
      # Spark configurations for development
      spark_config:
        spark.sql.adaptive.enabled: true
        spark.sql.adaptive.coalescePartitions.enabled: true
        spark.sql.adaptive.skewJoin.enabled: true
        spark.sql.execution.arrow.pyspark.enabled: true
        spark.sql.adaptive.advisoryPartitionSizeInBytes: 128MB
        
    prod:
      type: spark
      method: thrift
      schema: tdr_prod
      host: "{{ env_var('SPARK_HOST') }}"
      port: 10000
      user: "{{ env_var('SPARK_USER') }}"
      
      # Production Spark configurations
      spark_config:
        spark.sql.adaptive.enabled: true
        spark.sql.adaptive.coalescePartitions.enabled: true
        spark.sql.adaptive.skewJoin.enabled: true
        spark.sql.execution.arrow.pyspark.enabled: true
        spark.sql.adaptive.advisoryPartitionSizeInBytes: 256MB
        spark.sql.adaptive.maxRecordsPerFile: 1000000
        spark.sql.files.maxPartitionBytes: 1073741824  # 1GB
        
    test:
      type: spark
      method: session
      schema: tdr_test
      host: localhost
      port: 10000
      
      # Test environment configurations
      spark_config:
        spark.sql.adaptive.enabled: true
        spark.sql.adaptive.coalescePartitions.enabled: true
