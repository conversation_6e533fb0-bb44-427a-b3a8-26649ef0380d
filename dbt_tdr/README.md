# TDR dbt Project

## 项目概述

这是 TDR (Tungee Data Repository) 项目的 dbt 重构版本，旨在将原有的 PySpark RDD 作业转换为高性能的 Spark SQL 实现，并使用 dbt 进行项目管理和数据建模。

## 项目结构

```
dbt_tdr/
├── dbt_project.yml          # dbt 项目配置
├── profiles.yml             # 数据库连接配置
├── README.md               # 项目文档
├── models/                 # 数据模型
│   ├── staging/           # 数据清洗层 (ODS → Staging)
│   ├── intermediate/      # 数据整合层 (DWD)
│   ├── marts/            # 业务逻辑层 (DWM)
│   │   └── enterprise/   # 企业域模型
│   │       ├── risk/     # 风险分析
│   │       ├── promotion/ # 推广分析
│   │       └── certificate/ # 证书分析
│   └── data_marts/       # 数据服务层 (DWS)
├── macros/               # SQL 宏和工具函数
├── tests/                # 数据质量测试
├── seeds/                # 参考数据
├── snapshots/            # 数据快照
└── docs/                 # 项目文档
```

## 数据分层架构

### 1. Staging Layer (staging/)
- **目的**: 数据清洗和标准化
- **对应原系统**: ODS 层
- **特点**: 
  - 视图物化，节省存储空间
  - 基础数据清洗和格式标准化
  - 添加数据质量检查

### 2. Intermediate Layer (intermediate/)
- **目的**: 数据整合和预处理
- **对应原系统**: DWD 层
- **特点**:
  - 表物化，提升查询性能
  - 按日期分区
  - 生成业务主键和关联字段

### 3. Marts Layer (marts/)
- **目的**: 业务逻辑计算
- **对应原系统**: DWM 层
- **特点**:
  - 按业务域组织
  - 复杂业务逻辑实现
  - 支持增量更新

### 4. Data Marts Layer (data_marts/)
- **目的**: 面向应用的数据服务
- **对应原系统**: DWS 层
- **特点**:
  - 最终用户数据接口
  - 高度聚合的统计数据
  - 优化的查询性能

## 核心功能模块

### 企业推广分析 (Enterprise Promotion)
- `stg_promotion.sql` - 推广数据清洗
- `dwm_enterprise_promotion_competitor.sql` - 竞对关系分析
- `dwm_enterprise_promotion_link.sql` - 推广链接分析
- `dws_enterprise_promotion_stats.sql` - 推广统计汇总

### 企业风险分析 (Enterprise Risk)
- 待实现：风险评估模型
- 待实现：司法风险分析
- 待实现：合规风险监控

### 企业证书分析 (Enterprise Certificate)
- 待实现：证书有效性分析
- 待实现：证书到期预警
- 待实现：认证机构分析

## 性能优化特性

### 1. Delta Lake 支持
- 使用 Delta 格式存储，支持 ACID 事务
- 自动数据压缩和优化
- 时间旅行和版本控制

### 2. 智能分区
- 按日期分区，支持分区裁剪
- 动态分区数量调整
- 避免小文件问题

### 3. 自适应查询执行
- 启用 Spark SQL 自适应查询执行
- 动态分区合并
- 倾斜连接优化

### 4. 增量处理
- 支持增量模型更新
- 基于时间戳的增量逻辑
- 减少数据处理量

## 快速开始

### 1. 环境准备
```bash
# 安装 dbt-spark
pip install dbt-spark

# 验证安装
dbt --version
```

### 2. 配置连接
编辑 `~/.dbt/profiles.yml` 或使用项目中的 `profiles.yml`:
```yaml
tdr:
  target: dev
  outputs:
    dev:
      type: spark
      method: session
      schema: tdr_dev
```

### 3. 运行项目
```bash
# 进入项目目录
cd dbt_tdr

# 安装依赖
dbt deps

# 运行所有模型
dbt run

# 运行测试
dbt test

# 生成文档
dbt docs generate
dbt docs serve
```

### 4. 增量运行
```bash
# 只运行修改过的模型
dbt run --select state:modified+

# 运行特定标签的模型
dbt run --select tag:promotion

# 运行特定模型及其下游
dbt run --select stg_promotion+
```

## 数据质量保障

### 1. 内置测试
- 主键唯一性检查
- 非空值检查
- 数据新鲜度检查
- 引用完整性检查

### 2. 自定义测试
- 业务逻辑验证
- 数据一致性检查
- 性能基准测试

### 3. 数据血缘
- 自动生成数据血缘图
- 影响分析
- 变更追踪

## 迁移指南

### 从 PySpark RDD 到 dbt SQL

1. **识别业务逻辑**: 分析原有 RDD 操作的业务含义
2. **SQL 转换**: 将 RDD 转换操作转换为等价的 SQL
3. **性能优化**: 利用 Spark SQL 的优化器
4. **测试验证**: 确保结果一致性

### 迁移优先级
1. **高优先级**: 推广分析模块 (已完成)
2. **中优先级**: 风险分析模块
3. **低优先级**: 证书分析模块

## 监控和运维

### 1. 作业监控
- dbt 运行日志
- Spark UI 监控
- 数据质量报告

### 2. 性能调优
- 查询计划分析
- 分区策略优化
- 缓存策略调整

### 3. 故障处理
- 失败重试机制
- 数据回滚策略
- 告警通知

## 贡献指南

1. 遵循 dbt 最佳实践
2. 添加适当的测试和文档
3. 使用语义化的模型命名
4. 保持 SQL 代码的可读性

## 联系方式

如有问题或建议，请联系数据团队。
