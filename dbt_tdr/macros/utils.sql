-- macros/utils.sql
-- Common utility macros for TDR project

-- Macro to generate MD5 hash (equivalent to str_md5 function)
{% macro generate_md5(column_name, digit=32) %}
  {% if digit == 32 %}
    md5(cast({{ column_name }} as string))
  {% elif digit == 16 %}
    substr(md5(cast({{ column_name }} as string)), 9, 16)
  {% else %}
    {{ exceptions.raise_compiler_error("Invalid digit parameter. Must be 16 or 32.") }}
  {% endif %}
{% endmacro %}

-- Macro to generate base64 encoded MD5 hash
{% macro generate_md5_base64(column_name, digit=32) %}
  base64(unhex({{ generate_md5(column_name, digit) }}))
{% endmacro %}

-- Macro to convert arbitrary date format (equivalent to convert_arbitrary_date_format)
{% macro convert_date_format(date_column) %}
  case
    when {{ date_column }} rlike '^\\d{4}-\\d{2}-\\d{2}$' then to_date({{ date_column }}, 'yyyy-MM-dd')
    when {{ date_column }} rlike '^\\d{4}/\\d{2}/\\d{2}$' then to_date({{ date_column }}, 'yyyy/MM/dd')
    when {{ date_column }} rlike '^\\d{4}\\.\\d{2}\\.\\d{2}$' then to_date({{ date_column }}, 'yyyy.MM.dd')
    when {{ date_column }} rlike '^\\d{4}年\\d{2}月\\d{2}日$' then to_date({{ date_column }}, 'yyyy年MM月dd日')
    when {{ date_column }} rlike '^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$' then to_timestamp({{ date_column }}, 'yyyy-MM-dd HH:mm:ss')
    else null
  end
{% endmacro %}

-- Macro to calculate date difference in days
{% macro date_diff_days(start_date, end_date) %}
  datediff({{ end_date }}, {{ start_date }})
{% endmacro %}

-- Macro to get current date partition
{% macro get_current_date_partition() %}
  date_format(current_date(), 'yyyy-MM-dd')
{% endmacro %}

-- Macro to filter by date range
{% macro filter_by_date_range(date_column, start_date=none, end_date=none) %}
  {% if start_date and end_date %}
    {{ date_column }} between '{{ start_date }}' and '{{ end_date }}'
  {% elif start_date %}
    {{ date_column }} >= '{{ start_date }}'
  {% elif end_date %}
    {{ date_column }} <= '{{ end_date }}'
  {% else %}
    1=1
  {% endif %}
{% endmacro %}

-- Macro to handle null values in arrays
{% macro array_remove_nulls(array_column) %}
  filter({{ array_column }}, x -> x is not null)
{% endmacro %}

-- Macro to safely get array element
{% macro safe_array_get(array_column, index, default_value='null') %}
  case
    when size({{ array_column }}) > {{ index }} then {{ array_column }}[{{ index }}]
    else {{ default_value }}
  end
{% endmacro %}

-- Macro to calculate risk score based on weights
{% macro calculate_risk_score(anomaly_count, punishment_count, enforcement_count, dishonesty_count, bankruptcy_count) %}
  (
    coalesce({{ anomaly_count }}, 0) * {{ var('risk_score_weights.anomaly') }} +
    coalesce({{ punishment_count }}, 0) * {{ var('risk_score_weights.punishment') }} +
    coalesce({{ enforcement_count }}, 0) * {{ var('risk_score_weights.enforcement') }} +
    coalesce({{ dishonesty_count }}, 0) * {{ var('risk_score_weights.dishonesty') }} +
    coalesce({{ bankruptcy_count }}, 0) * {{ var('risk_score_weights.bankruptcy') }}
  )
{% endmacro %}

-- Macro to standardize enterprise name
{% macro standardize_enterprise_name(name_column) %}
  trim(
    regexp_replace(
      regexp_replace(
        regexp_replace({{ name_column }}, '\\s+', ' '),  -- Replace multiple spaces with single space
        '[（(].*[）)]', ''  -- Remove content in parentheses
      ),
      '有限责任?公司|股份有限公司|有限公司', '公司'  -- Standardize company suffixes
    )
  )
{% endmacro %}

-- Macro to extract year from date
{% macro extract_year(date_column) %}
  year({{ convert_date_format(date_column) }})
{% endmacro %}

-- Macro to check if string is valid (not null and not empty)
{% macro is_valid_string(column_name) %}
  ({{ column_name }} is not null and trim({{ column_name }}) != '')
{% endmacro %}

-- Macro to generate incremental where clause
{% macro incremental_where_clause(date_column='update_time') %}
  {% if is_incremental() %}
    where {{ date_column }} > (select max({{ date_column }}) from {{ this }})
  {% endif %}
{% endmacro %}

-- Macro to create partition column
{% macro add_partition_column(date_column='update_time') %}
  date_format({{ convert_date_format(date_column) }}, 'yyyy-MM-dd') as dt
{% endmacro %}
